.cyberpunk-city {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: radial-gradient(ellipse at center, #001122 0%, #000011 70%, #000000 100%);
  perspective: 1000px;
  transform-style: preserve-3d;
}

.cyberpunk-city.day {
  background: radial-gradient(ellipse at center, #003366 0%, #001133 70%, #000022 100%);
}

.city-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 10%, rgba(128, 255, 0, 0.05) 0%, transparent 50%);
  animation: atmosphericShift 20s ease-in-out infinite;
}

.atmosphere-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 17, 34, 0.8) 0%,
    rgba(0, 34, 68, 0.4) 50%,
    rgba(0, 17, 34, 0.9) 100%
  );
  pointer-events: none;
}

.cyber-ground {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  background:
    linear-gradient(90deg, transparent 49%, rgba(0, 255, 255, 0.3) 50%, transparent 51%),
    linear-gradient(0deg, transparent 49%, rgba(0, 255, 255, 0.3) 50%, transparent 51%);
  background-size: 50px 50px;
  animation: gridPulse 3s ease-in-out infinite;
  transform: rotateX(85deg) translateZ(-100px);
}

.cyberpunk-city::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, transparent 48%, rgba(0, 255, 255, 0.03) 49%, rgba(0, 255, 255, 0.03) 51%, transparent 52%),
    linear-gradient(-45deg, transparent 48%, rgba(255, 0, 128, 0.03) 49%, rgba(255, 0, 128, 0.03) 51%, transparent 52%);
  background-size: 20px 20px;
  pointer-events: none;
  z-index: 1;
  animation: gridPulse 4s ease-in-out infinite;
}

.cyberpunk-city::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(128, 255, 0, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
  animation: atmosphericGlow 6s ease-in-out infinite alternate;
}

/* Building Styles */
.buildings-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform-style: preserve-3d;
}

.building {
  position: absolute;
  bottom: 200px;
  background: linear-gradient(180deg, #001133 0%, #002244 50%, #001122 100%);
  border: 1px solid rgba(0, 255, 255, 0.3);
  transform-style: preserve-3d;
  transition: all 0.3s ease;
}

.building:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px var(--neon-color);
}

.building.glitch {
  animation: buildingGlitch 0.2s ease-in-out;
}

.building-structure {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
}

.building-face {
  position: absolute;
  width: 100%;
  height: 100%;
  background: inherit;
}

.windows {
  position: absolute;
  top: 10px;
  left: 5px;
  right: 5px;
  bottom: 10px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(8px, 1fr));
  grid-template-rows: repeat(auto-fit, minmax(8px, 1fr));
  gap: 3px;
}

.window {
  background: #001122;
  border: 1px solid rgba(0, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.window.lit {
  animation: windowFlicker 3s ease-in-out infinite;
}

.neon-strips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.neon-strip {
  position: absolute;
  background: var(--neon-color);
  box-shadow:
    0 0 10px var(--neon-color),
    inset 0 0 10px var(--neon-color);
  animation: neonPulse 2s ease-in-out infinite;
}

.neon-strip.horizontal {
  height: 3px;
  left: 0;
  right: 0;
}

.neon-strip.top {
  top: 0;
}

.neon-strip.vertical {
  width: 3px;
  top: 0;
  bottom: 0;
}

.neon-strip.left {
  left: 0;
}

.neon-strip.right {
  right: 0;
}

.antenna {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 20px;
}

.antenna-pole {
  width: 100%;
  height: 100%;
  background: #666;
  box-shadow: 0 0 5px #666;
}

.antenna-light {
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: #ff0000;
  border-radius: 50%;
  box-shadow: 0 0 10px #ff0000;
}

.antenna-light.blinking {
  animation: blink 1s ease-in-out infinite;
}

.building-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(ellipse at center, var(--neon-color) 0%, transparent 70%);
  opacity: 0.1;
  animation: buildingGlow 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes buildingGlitch {
  0%, 100% { transform: translateX(0); }
  20% { transform: translateX(-2px); }
  40% { transform: translateX(2px); }
  60% { transform: translateX(-1px); }
  80% { transform: translateX(1px); }
}

@keyframes windowFlicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
  75% { opacity: 0.9; }
}

@keyframes neonPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes buildingGlow {
  0%, 100% { opacity: 0.1; }
  50% { opacity: 0.3; }
}

@keyframes gridPulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes atmosphericGlow {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes atmosphericShift {
  0%, 100% { filter: hue-rotate(0deg); }
  33% { filter: hue-rotate(120deg); }
  66% { filter: hue-rotate(240deg); }
}

/* Flying Vehicles */
.vehicles-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  transform-style: preserve-3d;
}

.flying-vehicle {
  position: absolute;
  width: 20px;
  height: 8px;
  transform-style: preserve-3d;
}

.vehicle-body {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #333, #666);
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  position: relative;
}

.vehicle-lights {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.vehicle-lights .light {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: vehicleLightPulse 1s ease-in-out infinite;
}

.vehicle-lights .light.front {
  top: 2px;
  left: -2px;
  background: var(--vehicle-glow, #00ff00);
  box-shadow: 0 0 10px var(--vehicle-glow, #00ff00);
}

.vehicle-lights .light.rear {
  top: 2px;
  right: -2px;
  background: #ff0000;
  box-shadow: 0 0 8px #ff0000;
}

.vehicle-trail {
  position: absolute;
  top: 50%;
  right: 100%;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, var(--vehicle-glow, #00ff00), transparent);
  transform: translateY(-50%);
  opacity: 0.6;
  animation: trailFade 0.5s ease-out infinite;
}

@keyframes vehicleLightPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes trailFade {
  0% { opacity: 0.8; width: 30px; }
  100% { opacity: 0; width: 10px; }
}

/* Enhanced Flying Vehicles */
.flying-vehicle.police .vehicle-body {
  background: linear-gradient(45deg, #333, #666, #ff0000);
}

.flying-vehicle.cargo {
  transform-origin: center;
}

.flying-vehicle.cargo .cargo-container {
  position: absolute;
  top: -5px;
  left: -10px;
  right: -10px;
  bottom: -5px;
  background: #444;
  border: 1px solid #666;
  border-radius: 2px;
}

.flying-vehicle.landing {
  animation: landingSequence 2s ease-in-out;
}

.vehicle-lights .light.police-strobe {
  position: absolute;
  top: 1px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: #ff0000;
  border-radius: 50%;
  animation: policeStrobe 0.5s ease-in-out infinite;
}

@keyframes landingSequence {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.5); }
}

@keyframes policeStrobe {
  0%, 50% { opacity: 1; background: #ff0000; }
  25%, 75% { opacity: 1; background: #0000ff; }
  12.5%, 37.5%, 62.5%, 87.5% { opacity: 0; }
}

/* Ground Traffic */
.ground-traffic-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 250px;
  pointer-events: none;
  z-index: 3;
}

.ground-vehicle {
  position: absolute;
  width: 40px;
  height: 20px;
  z-index: 3;
}

.ground-vehicle.truck {
  width: 60px;
  height: 25px;
}

.ground-vehicle.taxi {
  filter: hue-rotate(60deg);
}

.vehicle-chassis {
  width: 100%;
  height: 100%;
  background: var(--vehicle-color);
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  position: relative;
}

.headlights {
  position: absolute;
  left: -2px;
  top: 3px;
  bottom: 3px;
  width: 4px;
  background: #ffffff;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.headlights.on {
  box-shadow: -10px 0 15px rgba(255, 255, 255, 0.8);
}

.headlights.off {
  background: #666;
  box-shadow: none;
}

.taillights {
  position: absolute;
  right: -2px;
  top: 5px;
  bottom: 5px;
  width: 3px;
  background: #ff0000;
  border-radius: 1px;
  box-shadow: 5px 0 8px rgba(255, 0, 0, 0.6);
  animation: tailLightPulse 2s ease-in-out infinite;
}

@keyframes tailLightPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* Pedestrians */
.pedestrians-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 250px;
  pointer-events: none;
  z-index: 4;
}

.pedestrian {
  position: absolute;
  width: 8px;
  height: 15px;
  z-index: 4;
}

.pedestrian-body {
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #333 0%, #666 50%, #333 100%);
  border-radius: 4px 4px 2px 2px;
  position: relative;
}

.pedestrian.walking .pedestrian-body {
  animation: walkingMotion 0.8s ease-in-out infinite;
}

.pedestrian-shadow {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  transform: scaleY(0.5);
}

@keyframes walkingMotion {
  0%, 100% { transform: translateY(0) scaleY(1); }
  50% { transform: translateY(-1px) scaleY(1.1); }
}

/* Holographic Billboards */
.billboards-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  transform-style: preserve-3d;
}

.holo-billboard {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid var(--billboard-color);
  border-radius: 10px;
  overflow: hidden;
  transform-style: preserve-3d;
}

.holo-billboard.large {
  width: 200px;
  height: 100px;
}

.holo-billboard.medium {
  width: 150px;
  height: 75px;
}

.holo-billboard.small {
  width: 100px;
  height: 50px;
}

.billboard-frame {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--billboard-color), transparent, var(--billboard-color));
  border-radius: 12px;
  animation: frameGlow 3s ease-in-out infinite;
  z-index: -1;
}

.billboard-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.billboard-text {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: var(--billboard-color);
  text-shadow: 0 0 10px var(--billboard-color);
  font-size: 14px;
  text-align: center;
  z-index: 2;
}

.holo-billboard.large .billboard-text {
  font-size: 18px;
}

.holo-billboard.medium .billboard-text {
  font-size: 14px;
}

.holo-billboard.small .billboard-text {
  font-size: 10px;
}

.billboard-scanlines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    transparent 50%,
    rgba(255, 255, 255, 0.1) 50%
  );
  background-size: 100% 4px;
  animation: scanlineMove 0.1s linear infinite;
  z-index: 1;
}

.billboard-glow {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(ellipse at center, var(--billboard-color) 0%, transparent 70%);
  opacity: 0.2;
  animation: billboardGlow 4s ease-in-out infinite;
  z-index: -2;
}

.holo-billboard.glitching .billboard-text {
  animation: textGlitch 0.2s ease-in-out;
}

@keyframes frameGlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes billboardGlow {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

@keyframes textGlitch {
  0%, 100% { transform: translateX(0); }
  20% { transform: translateX(-2px); }
  40% { transform: translateX(2px); }
  60% { transform: translateX(-1px); }
  80% { transform: translateX(1px); }
}

/* Enhanced Billboard Features */
.ad-progress {
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 3px;
}

.progress-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.progress-dot.active {
  background: var(--billboard-color);
  box-shadow: 0 0 8px var(--billboard-color);
}

/* Data Streams */
.data-streams {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.data-stream {
  position: absolute;
  top: -20px;
  width: 2px;
  height: 20px;
  background: var(--stream-color);
  font-family: 'Courier New', monospace;
  font-size: 8px;
  color: var(--stream-color);
  text-shadow: 0 0 5px var(--stream-color);
  animation: dataStreamFlow var(--stream-speed) linear infinite;
  opacity: 0.8;
}

@keyframes dataStreamFlow {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(calc(100vh + 20px));
    opacity: 0;
  }
}

/* Security Drones */
.drones-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 8;
}

.security-drone {
  position: absolute;
  width: 30px;
  height: 30px;
  z-index: 8;
}

.drone-body {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 8px;
  background: linear-gradient(45deg, #333, #666);
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.drone-rotors {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.rotor {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: rotorSpin 0.1s linear infinite;
}

.rotor:nth-child(1) { top: 2px; left: 2px; }
.rotor:nth-child(2) { top: 2px; right: 2px; }
.rotor:nth-child(3) { bottom: 2px; left: 2px; }
.rotor:nth-child(4) { bottom: 2px; right: 2px; }

.scan-beam {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 100px;
  background: linear-gradient(180deg, var(--drone-color), transparent);
  opacity: 0;
  transition: all 0.3s ease;
}

.security-drone.scanning .scan-beam {
  opacity: 0.8;
  animation: scanBeamPulse 1s ease-in-out infinite;
}

.drone-lights {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  background: var(--drone-color);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--drone-color);
  animation: droneLightPulse 2s ease-in-out infinite;
}

.security-drone.alert .drone-lights {
  animation: alertFlash 0.3s ease-in-out infinite;
}

@keyframes rotorSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes scanBeamPulse {
  0%, 100% { opacity: 0.8; width: 2px; }
  50% { opacity: 1; width: 4px; }
}

@keyframes droneLightPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes alertFlash {
  0%, 50% { opacity: 1; }
  25%, 75% { opacity: 0.3; }
}

/* Emergency Events */
.emergency-event {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 50;
  background: rgba(255, 0, 0, 0.9);
  color: white;
  padding: 15px 20px;
  border-radius: 10px;
  border: 2px solid #ff0000;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  animation: emergencyPulse 1s ease-in-out infinite;
  box-shadow: 0 0 20px #ff0000;
}

.emergency-event.fire {
  background: rgba(255, 69, 0, 0.9);
  border-color: #ff4500;
  box-shadow: 0 0 20px #ff4500;
}

.emergency-event.police {
  background: rgba(0, 0, 255, 0.9);
  border-color: #0000ff;
  box-shadow: 0 0 20px #0000ff;
}

.emergency-event.medical {
  background: rgba(255, 255, 255, 0.9);
  color: #ff0000;
  border-color: #ffffff;
  box-shadow: 0 0 20px #ffffff;
}

.emergency-lights {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 15px;
  animation: emergencyLights 0.5s ease-in-out infinite;
  z-index: -1;
}

@keyframes emergencyPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes emergencyLights {
  0%, 50% { background: rgba(255, 0, 0, 0.3); }
  25%, 75% { background: rgba(0, 0, 255, 0.3); }
}

/* Traffic Lights */
.traffic-lights-container {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 250px;
  pointer-events: none;
  z-index: 5;
}

.traffic-light {
  position: absolute;
  width: 12px;
  height: 36px;
  background: #333;
  border-radius: 6px;
  border: 1px solid #666;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 2px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

.traffic-light .light {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.traffic-light .light.red {
  background: #ff0000;
}

.traffic-light .light.yellow {
  background: #ffff00;
}

.traffic-light .light.green {
  background: #00ff00;
}

.traffic-light .light.active {
  opacity: 1;
  box-shadow: 0 0 10px currentColor;
}

/* City Activity States */
.cyberpunk-city.busy .flying-vehicle {
  animation-duration: 0.8s;
}

.cyberpunk-city.rush_hour .flying-vehicle {
  animation-duration: 0.6s;
}

.cyberpunk-city.rush_hour .ground-vehicle {
  animation-duration: 0.7s;
}

.cyberpunk-city.quiet .flying-vehicle {
  animation-duration: 1.5s;
  opacity: 0.7;
}

.cyberpunk-city.quiet .ground-vehicle {
  animation-duration: 1.8s;
  opacity: 0.6;
}

.cyberpunk-city.busy .building {
  animation: buildingBusy 3s ease-in-out infinite;
}

.cyberpunk-city.rush_hour .building {
  animation: buildingRushHour 2s ease-in-out infinite;
}

@keyframes buildingBusy {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.2); }
}

@keyframes buildingRushHour {
  0%, 100% { filter: brightness(1) saturate(1); }
  50% { filter: brightness(1.3) saturate(1.2); }
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .flying-vehicle {
    transform: scale(0.8);
  }

  .ground-vehicle {
    transform: scale(0.9);
  }

  .security-drone {
    transform: scale(0.7);
  }
}

@media (max-width: 768px) {
  .emergency-event {
    top: 10px;
    right: 10px;
    padding: 10px 15px;
    font-size: 12px;
  }

  .traffic-light {
    transform: scale(0.8);
  }

  .pedestrian {
    transform: scale(0.8);
  }
}

/* Cyberpunk UI overlay */
.cyberpunk-city .ui-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  color: #00ffff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  text-shadow: 0 0 10px #00ffff;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border: 1px solid #00ffff;
  border-radius: 5px;
}

.cyberpunk-city .ui-overlay::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #00ffff, #ff0080, #80ff00, #00ffff);
  border-radius: 5px;
  z-index: -1;
  animation: borderGlow 2s linear infinite;
}

@keyframes borderGlow {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

/* Scanlines effect */
.cyberpunk-city .scanlines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    transparent 50%, 
    rgba(0, 255, 255, 0.03) 50%
  );
  background-size: 100% 4px;
  pointer-events: none;
  z-index: 2;
  animation: scanlineMove 0.1s linear infinite;
}

@keyframes scanlineMove {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(4px);
  }
}

/* Glitch effect for text */
.cyberpunk-city .glitch-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Courier New', monospace;
  font-size: 24px;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 
    0 0 5px #00ffff,
    0 0 10px #00ffff,
    0 0 15px #00ffff;
  z-index: 10;
  animation: glitch 2s infinite;
}

@keyframes glitch {
  0%, 90%, 100% {
    transform: translate(-50%, -50%);
    filter: hue-rotate(0deg);
  }
  10% {
    transform: translate(-51%, -50%);
    filter: hue-rotate(90deg);
  }
  20% {
    transform: translate(-49%, -50%);
    filter: hue-rotate(180deg);
  }
  30% {
    transform: translate(-50%, -49%);
    filter: hue-rotate(270deg);
  }
  40% {
    transform: translate(-50%, -51%);
    filter: hue-rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .cyberpunk-city .ui-overlay {
    font-size: 10px;
    padding: 8px;
  }
  
  .cyberpunk-city .glitch-text {
    font-size: 18px;
  }
}

/* Loading animation */
.cyberpunk-city .loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 100;
}

.cyberpunk-city .loading::before {
  content: 'INITIALIZING NEURAL NETWORK...';
  color: #00ffff;
  font-family: 'Courier New', monospace;
  font-size: 16px;
  text-shadow: 0 0 10px #00ffff;
  animation: loadingPulse 1s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* Particle System */
.particle-system {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
}

.particle {
  position: absolute;
  pointer-events: none;
}

.particle.rain {
  background: linear-gradient(180deg, rgba(0, 255, 255, 0.8), rgba(0, 255, 255, 0.2));
  width: 1px;
  border-radius: 1px;
}

.particle.snow {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
}

/* Control Panel */
.control-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  gap: 10px;
}

.cyber-button {
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #00ffff;
  color: #00ffff;
  padding: 10px 20px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  cursor: pointer;
  border-radius: 5px;
  transition: all 0.3s ease;
  text-shadow: 0 0 5px #00ffff;
  position: relative;
  overflow: hidden;
}

.cyber-button:hover {
  background: rgba(0, 255, 255, 0.1);
  box-shadow: 0 0 20px #00ffff;
  transform: translateY(-2px);
}

.cyber-button:active {
  transform: translateY(0);
}

.cyber-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.cyber-button:hover::before {
  left: 100%;
}

/* Enhanced UI Overlay */
.ui-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  color: #00ffff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  text-shadow: 0 0 10px #00ffff;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border: 2px solid #00ffff;
  border-radius: 10px;
  backdrop-filter: blur(5px);
}

.system-info div {
  margin-bottom: 5px;
  position: relative;
}

.system-info div::before {
  content: '> ';
  color: #ff0080;
  text-shadow: 0 0 5px #ff0080;
}

/* Weather Effects */
.cyberpunk-city.rain {
  filter: brightness(0.7) contrast(1.2);
}

.cyberpunk-city.snow {
  filter: brightness(1.1) contrast(0.9) hue-rotate(30deg);
}

/* Additional Animations */
@keyframes dataStream {
  0% { transform: translateY(-100vh); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ui-overlay {
    font-size: 10px;
    padding: 10px;
  }

  .cyber-button {
    padding: 8px 15px;
    font-size: 10px;
  }

  .holo-billboard.large {
    width: 150px;
    height: 75px;
  }

  .holo-billboard.medium {
    width: 100px;
    height: 50px;
  }

  .building {
    transform: scale(0.8);
  }
}

@media (max-width: 480px) {
  .control-panel {
    bottom: 10px;
    right: 10px;
    flex-direction: column;
  }

  .ui-overlay {
    top: 10px;
    left: 10px;
    font-size: 8px;
    padding: 8px;
  }
}
