import React, { useState } from 'react'
import Header from './components/Header'
import Hero from './components/Hero'
import Services from './components/Services'
import Portfolio from './components/Portfolio'
import About from './components/About'
import Contact from './components/Contact'
import Footer from './components/Footer'
import CyberpunkCity from './components/CyberpunkCity'
import './App.css'

function App() {
  const [showCyberpunkCity, setShowCyberpunkCity] = useState(false)

  if (showCyberpunkCity) {
    return <CyberpunkCity />
  }

  return (
    <div className="App">
      <Header />
      <main>
        <Hero />
        <div style={{
          padding: '20px',
          textAlign: 'center',
          background: 'linear-gradient(45deg, #001122, #002244)',
          margin: '20px 0'
        }}>
          <button
            onClick={() => setShowCyberpunkCity(true)}
            style={{
              background: 'linear-gradient(45deg, #00ffff, #ff0080)',
              border: 'none',
              padding: '15px 30px',
              fontSize: '18px',
              fontWeight: 'bold',
              color: 'white',
              borderRadius: '10px',
              cursor: 'pointer',
              textShadow: '0 0 10px rgba(0,0,0,0.5)',
              boxShadow: '0 0 20px rgba(0, 255, 255, 0.5)',
              transition: 'all 0.3s ease'
            }}
            onMouseOver={(e) => {
              e.target.style.transform = 'scale(1.1)'
              e.target.style.boxShadow = '0 0 30px rgba(0, 255, 255, 0.8)'
            }}
            onMouseOut={(e) => {
              e.target.style.transform = 'scale(1)'
              e.target.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.5)'
            }}
          >
            🌃 ENTER CYBERPUNK CITY 🌃
          </button>
          <p style={{ color: '#00ffff', marginTop: '10px', fontFamily: 'monospace' }}>
            Experience a detailed animated miniature cyberpunk metropolis
          </p>
        </div>
        <Services />
        <Portfolio />
        <About />
        <Contact />
      </main>
      <Footer />
    </div>
  )
}

export default App
