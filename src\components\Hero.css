.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 70px;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(245, 158, 11, 0.02) 100%);
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: calc(100vh - 140px);
}

.hero-text {
  z-index: 1;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.hero-actions .btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.hero-stats {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.stat {
  text-align: left;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.hero-card {
  background: var(--bg-primary);
  border-radius: 1.5rem;
  box-shadow: var(--shadow-xl);
  width: 400px;
  height: 300px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  position: relative;
  transform: rotate(-5deg);
  transition: transform 0.3s ease;
}

.hero-card:hover {
  transform: rotate(-2deg) scale(1.02);
}

.card-header {
  background: var(--bg-secondary);
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.card-dots {
  display: flex;
  gap: 0.5rem;
}

.card-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--text-light);
}

.card-dots span:nth-child(1) {
  background: #ef4444;
}

.card-dots span:nth-child(2) {
  background: #f59e0b;
}

.card-dots span:nth-child(3) {
  background: #10b981;
}

.card-content {
  padding: 2rem;
  height: calc(100% - 60px);
  position: relative;
}

.design-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.element {
  position: absolute;
  border-radius: 0.5rem;
  animation: float 6s ease-in-out infinite;
}

.element-1 {
  width: 80px;
  height: 20px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--secondary-color), #f97316);
  top: 40%;
  right: 20%;
  border-radius: 50%;
  animation-delay: 1s;
}

.element-3 {
  width: 100px;
  height: 15px;
  background: linear-gradient(90deg, #10b981, #059669);
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.element-4 {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  bottom: 20%;
  right: 15%;
  border-radius: 0.25rem;
  animation-delay: 3s;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  border-radius: 50%;
  animation: floatAround 8s ease-in-out infinite;
}

.floating-1 {
  width: 20px;
  height: 20px;
  background: rgba(99, 102, 241, 0.3);
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.floating-2 {
  width: 30px;
  height: 30px;
  background: rgba(245, 158, 11, 0.3);
  top: 70%;
  right: 10%;
  animation-delay: 2s;
}

.floating-3 {
  width: 15px;
  height: 15px;
  background: rgba(16, 185, 129, 0.3);
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes floatAround {
  0%, 100% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10px, -10px);
  }
  50% {
    transform: translate(-5px, -20px);
  }
  75% {
    transform: translate(-10px, 10px);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    gap: 3rem;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-card {
    width: 350px;
    height: 250px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding-top: 70px;
    min-height: auto;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
    max-width: none;
  }

  .hero-actions {
    justify-content: center;
    margin-bottom: 2rem;
  }

  .hero-stats {
    justify-content: center;
    gap: 1.5rem;
  }

  .stat {
    text-align: center;
  }

  .hero-card {
    width: 300px;
    height: 200px;
  }

  .card-content {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .hero-actions .btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .hero-stats {
    gap: 1rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-label {
    font-size: 0.75rem;
  }

  .hero-card {
    width: 250px;
    height: 180px;
  }
}
