import React, { useRef, useMemo, useEffect, useState } from 'react'
import './CyberpunkCity.css'

// Building component with detailed CSS-based 3D effect
function Building({ style, height, neonColor, hasAntenna = true, windowPattern = 'grid' }) {
  const [glitchActive, setGlitchActive] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.95) {
        setGlitchActive(true)
        setTimeout(() => setGlitchActive(false), 200)
      }
    }, 1000)
    return () => clearInterval(interval)
  }, [])

  const windows = useMemo(() => {
    const windowArray = []
    const floors = Math.floor(height / 20)
    const windowsPerFloor = 4 + Math.floor(Math.random() * 4)

    for (let floor = 0; floor < floors; floor++) {
      for (let window = 0; window < windowsPerFloor; window++) {
        windowArray.push({
          id: `${floor}-${window}`,
          lit: Math.random() > 0.3,
          color: Math.random() > 0.8 ? '#ff0080' : '#ffff80'
        })
      }
    }
    return windowArray
  }, [height])

  return (
    <div
      className={`building ${glitchActive ? 'glitch' : ''}`}
      style={{
        ...style,
        height: `${height}px`,
        '--neon-color': neonColor
      }}
    >
      <div className="building-structure">
        <div className="building-face front">
          <div className="windows">
            {windows.map((window, index) => (
              <div
                key={window.id}
                className={`window ${window.lit ? 'lit' : 'dark'}`}
                style={{
                  backgroundColor: window.lit ? window.color : '#001122',
                  boxShadow: window.lit ? `0 0 10px ${window.color}` : 'none'
                }}
              />
            ))}
          </div>
          <div className="neon-strips">
            <div className="neon-strip horizontal top" />
            <div className="neon-strip vertical left" />
            <div className="neon-strip vertical right" />
          </div>
        </div>

        {hasAntenna && (
          <div className="antenna">
            <div className="antenna-pole" />
            <div className="antenna-light blinking" />
          </div>
        )}

        <div className="building-glow" />

        {/* Data streams for active buildings */}
        {Math.random() > 0.6 && (
          <DataStream buildingId={style.left} windowCount={windows.length} />
        )}
      </div>
    </div>
  )
}

// Flying vehicle component with enhanced dynamics
function FlyingVehicle({ delay = 0, speed = 1, path = 'circular', vehicleType = 'standard' }) {
  const [position, setPosition] = useState({ x: 0, y: 0, z: 0 })
  const [rotation, setRotation] = useState(0)
  const [isLanding, setIsLanding] = useState(false)
  const [trailIntensity, setTrailIntensity] = useState(1)

  useEffect(() => {
    let animationId
    const startTime = Date.now() + delay * 1000

    const animate = () => {
      const elapsed = (Date.now() - startTime) / 1000
      if (elapsed > 0) {
        const time = elapsed * speed

        let x, y, z, rot = 0

        // Landing sequence every 30 seconds
        const landingCycle = (time % 30) / 30
        const isInLandingPhase = landingCycle > 0.8 && landingCycle < 0.95
        setIsLanding(isInLandingPhase)

        if (path === 'circular') {
          x = Math.sin(time * 0.5) * 300
          z = Math.cos(time * 0.3) * 250
          y = isInLandingPhase ? 50 + Math.sin(landingCycle * 20) * 20 : 100 + Math.sin(time * 2) * 30
          rot = time * 0.5
        } else if (path === 'figure8') {
          x = Math.sin(time * 0.8) * 200
          z = Math.sin(time * 0.4) * 300
          y = isInLandingPhase ? 60 + Math.cos(landingCycle * 15) * 15 : 120 + Math.cos(time * 1.5) * 20
          rot = Math.atan2(Math.cos(time * 0.8), Math.sin(time * 0.4))
        } else if (path === 'patrol') {
          // Police patrol pattern
          const patrolTime = time * 0.3
          x = Math.sin(patrolTime) * 400 + Math.sin(patrolTime * 3) * 50
          z = Math.cos(patrolTime) * 300 + Math.cos(patrolTime * 2) * 80
          y = 150 + Math.sin(time * 4) * 10
          rot = patrolTime
        } else {
          x = Math.sin(time * 0.3) * 350
          z = Math.cos(time * 0.7) * 200
          y = isInLandingPhase ? 40 + Math.sin(landingCycle * 25) * 25 : 80 + Math.sin(time * 3) * 40
          rot = time * 0.3
        }

        setPosition({ x, y, z })
        setRotation(rot)
        setTrailIntensity(isInLandingPhase ? 0.3 : 1)
      }
      animationId = requestAnimationFrame(animate)
    }

    animate()
    return () => cancelAnimationFrame(animationId)
  }, [delay, speed, path])

  return (
    <div
      className={`flying-vehicle ${vehicleType} ${isLanding ? 'landing' : ''}`}
      style={{
        transform: `translate3d(${position.x}px, ${-position.y}px, ${position.z}px) rotateY(${rotation}rad)`,
        '--vehicle-glow': vehicleType === 'police' ? '#ff0000' : vehicleType === 'cargo' ? '#ffff00' : '#00ff00',
        '--trail-intensity': trailIntensity
      }}
    >
      <div className="vehicle-body" />
      <div className="vehicle-lights">
        <div className="light front" />
        <div className="light rear" />
        {vehicleType === 'police' && <div className="light police-strobe" />}
      </div>
      <div className="vehicle-trail" />
      {vehicleType === 'cargo' && <div className="cargo-container" />}
    </div>
  )
}

// Ground traffic component
function GroundVehicle({ lane = 0, direction = 1, speed = 1, vehicleType = 'car' }) {
  const [position, setPosition] = useState(-100)
  const [lights, setLights] = useState(true)

  useEffect(() => {
    let animationId
    const startTime = Date.now()

    const animate = () => {
      const elapsed = (Date.now() - startTime) / 1000
      const newPos = (elapsed * speed * direction * 50) % (window.innerWidth + 200) - 100
      setPosition(newPos)

      // Random brake lights
      if (Math.random() > 0.995) {
        setLights(false)
        setTimeout(() => setLights(true), 500)
      }

      animationId = requestAnimationFrame(animate)
    }

    animate()
    return () => cancelAnimationFrame(animationId)
  }, [lane, direction, speed])

  return (
    <div
      className={`ground-vehicle ${vehicleType}`}
      style={{
        transform: `translateX(${position}px) ${direction === -1 ? 'scaleX(-1)' : ''}`,
        bottom: `${200 + lane * 15}px`,
        '--vehicle-color': vehicleType === 'truck' ? '#333' : vehicleType === 'taxi' ? '#ffff00' : '#666'
      }}
    >
      <div className="vehicle-chassis" />
      <div className={`headlights ${lights ? 'on' : 'off'}`} />
      <div className="taillights" />
    </div>
  )
}

// Pedestrian component
function Pedestrian({ startX, targetX, speed = 1 }) {
  const [position, setPosition] = useState(startX)
  const [isWalking, setIsWalking] = useState(true)

  useEffect(() => {
    let animationId
    const direction = targetX > startX ? 1 : -1

    const animate = () => {
      setPosition(prev => {
        const newPos = prev + direction * speed * 0.5
        if ((direction === 1 && newPos >= targetX) || (direction === -1 && newPos <= targetX)) {
          setIsWalking(false)
          setTimeout(() => {
            setPosition(startX)
            setIsWalking(true)
          }, 2000 + Math.random() * 3000)
          return prev
        }
        return newPos
      })

      if (isWalking) {
        animationId = requestAnimationFrame(animate)
      }
    }

    if (isWalking) {
      animate()
    }

    return () => cancelAnimationFrame(animationId)
  }, [startX, targetX, speed, isWalking])

  return (
    <div
      className={`pedestrian ${isWalking ? 'walking' : 'standing'}`}
      style={{
        transform: `translateX(${position}px)`,
        bottom: '200px'
      }}
    >
      <div className="pedestrian-body" />
      <div className="pedestrian-shadow" />
    </div>
  )
}

// Enhanced holographic billboard with dynamic content
function HoloBillboard({ text, position, color = '#00ffff', size = 'large' }) {
  const [glitchText, setGlitchText] = useState(text)
  const [isGlitching, setIsGlitching] = useState(false)
  const [currentAd, setCurrentAd] = useState(0)

  const advertisements = [
    text,
    'NEURAL IMPLANTS',
    'QUANTUM COFFEE',
    'CYBER SECURITY',
    'HOLO DATING',
    'MEMORY WIPE',
    'DIGITAL SOULS'
  ]

  useEffect(() => {
    // Cycle through advertisements
    const adInterval = setInterval(() => {
      setCurrentAd(prev => (prev + 1) % advertisements.length)
    }, 5000)

    // Glitch effects
    const glitchInterval = setInterval(() => {
      if (Math.random() > 0.85) {
        setIsGlitching(true)
        const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'
        let glitched = advertisements[currentAd].split('').map(char =>
          Math.random() > 0.6 ? glitchChars[Math.floor(Math.random() * glitchChars.length)] : char
        ).join('')
        setGlitchText(glitched)

        setTimeout(() => {
          setGlitchText(advertisements[currentAd])
          setIsGlitching(false)
        }, 200)
      }
    }, 1500)

    return () => {
      clearInterval(adInterval)
      clearInterval(glitchInterval)
    }
  }, [currentAd])

  useEffect(() => {
    setGlitchText(advertisements[currentAd])
  }, [currentAd])

  return (
    <div
      className={`holo-billboard ${size} ${isGlitching ? 'glitching' : ''}`}
      style={{
        ...position,
        '--billboard-color': color
      }}
    >
      <div className="billboard-frame" />
      <div className="billboard-content">
        <div className="billboard-text">{glitchText}</div>
        <div className="billboard-scanlines" />
        <div className="ad-progress">
          {advertisements.map((_, index) => (
            <div
              key={index}
              className={`progress-dot ${index === currentAd ? 'active' : ''}`}
            />
          ))}
        </div>
      </div>
      <div className="billboard-glow" />
    </div>
  )
}

// Data stream component for building windows
function DataStream({ buildingId, windowCount }) {
  const [streams, setStreams] = useState([])

  useEffect(() => {
    const generateStreams = () => {
      const newStreams = []
      for (let i = 0; i < Math.min(windowCount, 20); i++) {
        if (Math.random() > 0.7) {
          newStreams.push({
            id: i,
            data: generateRandomData(),
            speed: 0.5 + Math.random() * 2,
            color: ['#00ff00', '#00ffff', '#ff0080'][Math.floor(Math.random() * 3)]
          })
        }
      }
      setStreams(newStreams)
    }

    generateStreams()
    const interval = setInterval(generateStreams, 3000 + Math.random() * 2000)

    return () => clearInterval(interval)
  }, [windowCount])

  const generateRandomData = () => {
    const chars = '01'
    return Array.from({length: 10}, () => chars[Math.floor(Math.random() * chars.length)]).join('')
  }

  return (
    <div className="data-streams">
      {streams.map(stream => (
        <div
          key={stream.id}
          className="data-stream"
          style={{
            '--stream-color': stream.color,
            '--stream-speed': `${stream.speed}s`,
            left: `${(stream.id % 4) * 25}%`,
            animationDelay: `${Math.random() * 2}s`
          }}
        >
          {stream.data}
        </div>
      ))}
    </div>
  )
}

// Security drone component
function SecurityDrone({ patrolArea }) {
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isScanning, setIsScanning] = useState(false)
  const [alertLevel, setAlertLevel] = useState('normal')

  useEffect(() => {
    let animationId
    const startTime = Date.now()

    const animate = () => {
      const elapsed = (Date.now() - startTime) / 1000
      const time = elapsed * 0.3

      // Patrol pattern
      const x = Math.sin(time) * patrolArea.width + patrolArea.centerX
      const y = Math.cos(time * 1.3) * patrolArea.height + patrolArea.centerY

      setPosition({ x, y })

      // Random scanning behavior
      if (Math.random() > 0.98) {
        setIsScanning(true)
        setAlertLevel(Math.random() > 0.8 ? 'alert' : 'scanning')
        setTimeout(() => {
          setIsScanning(false)
          setAlertLevel('normal')
        }, 2000)
      }

      animationId = requestAnimationFrame(animate)
    }

    animate()
    return () => cancelAnimationFrame(animationId)
  }, [patrolArea])

  return (
    <div
      className={`security-drone ${alertLevel} ${isScanning ? 'scanning' : ''}`}
      style={{
        transform: `translate3d(${position.x}px, ${position.y}px, 0)`,
        '--drone-color': alertLevel === 'alert' ? '#ff0000' : '#00ffff'
      }}
    >
      <div className="drone-body" />
      <div className="drone-rotors">
        <div className="rotor" />
        <div className="rotor" />
        <div className="rotor" />
        <div className="rotor" />
      </div>
      <div className="scan-beam" />
      <div className="drone-lights" />
    </div>
  )
}

// Particle system for atmospheric effects
function ParticleSystem({ type = 'rain', density = 50 }) {
  const [particles, setParticles] = useState([])

  useEffect(() => {
    const newParticles = []
    for (let i = 0; i < density; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        speed: 1 + Math.random() * 3,
        opacity: 0.3 + Math.random() * 0.7,
        size: type === 'rain' ? 1 + Math.random() * 2 : 2 + Math.random() * 4
      })
    }
    setParticles(newParticles)
  }, [density, type])

  useEffect(() => {
    const animateParticles = () => {
      setParticles(prev => prev.map(particle => ({
        ...particle,
        y: particle.y > window.innerHeight ? -10 : particle.y + particle.speed,
        x: type === 'snow' ? particle.x + Math.sin(Date.now() * 0.001 + particle.id) * 0.5 : particle.x
      })))
    }

    const interval = setInterval(animateParticles, 50)
    return () => clearInterval(interval)
  }, [type])

  return (
    <div className="particle-system">
      {particles.map(particle => (
        <div
          key={particle.id}
          className={`particle ${type}`}
          style={{
            left: `${particle.x}px`,
            top: `${particle.y}px`,
            opacity: particle.opacity,
            width: `${particle.size}px`,
            height: type === 'rain' ? `${particle.size * 10}px` : `${particle.size}px`
          }}
        />
      ))}
    </div>
  )
}

// Emergency event system
function EmergencyEvent() {
  const [isActive, setIsActive] = useState(false)
  const [eventType, setEventType] = useState('none')

  useEffect(() => {
    const triggerEvent = () => {
      if (Math.random() > 0.95) {
        const events = ['fire', 'police', 'medical', 'security']
        setEventType(events[Math.floor(Math.random() * events.length)])
        setIsActive(true)

        setTimeout(() => {
          setIsActive(false)
          setEventType('none')
        }, 5000 + Math.random() * 10000)
      }
    }

    const interval = setInterval(triggerEvent, 3000)
    return () => clearInterval(interval)
  }, [])

  if (!isActive) return null

  return (
    <div className={`emergency-event ${eventType}`}>
      <div className="emergency-lights" />
      <div className="emergency-message">
        {eventType === 'fire' && '🔥 FIRE DETECTED - SECTOR 7'}
        {eventType === 'police' && '🚨 SECURITY BREACH - LOCKDOWN'}
        {eventType === 'medical' && '🏥 MEDICAL EMERGENCY - DISPATCH'}
        {eventType === 'security' && '⚠️ UNAUTHORIZED ACCESS - ALERT'}
      </div>
    </div>
  )
}

// Traffic light system
function TrafficLight({ position, id }) {
  const [currentLight, setCurrentLight] = useState('green')

  useEffect(() => {
    const cycle = () => {
      setCurrentLight(prev => {
        switch(prev) {
          case 'green': return 'yellow'
          case 'yellow': return 'red'
          case 'red': return 'green'
          default: return 'green'
        }
      })
    }

    const interval = setInterval(cycle, 3000 + Math.random() * 2000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div
      className="traffic-light"
      style={{
        ...position,
        '--current-light': currentLight
      }}
    >
      <div className={`light red ${currentLight === 'red' ? 'active' : ''}`} />
      <div className={`light yellow ${currentLight === 'yellow' ? 'active' : ''}`} />
      <div className={`light green ${currentLight === 'green' ? 'active' : ''}`} />
    </div>
  )
}

// Main cyberpunk city component
export default function CyberpunkCity() {
  const [timeOfDay, setTimeOfDay] = useState('night')
  const [weatherEffect, setWeatherEffect] = useState('clear')
  const [cityActivity, setCityActivity] = useState('normal')
  const [soundEnabled, setSoundEnabled] = useState(false)

  const buildings = useMemo(() => {
    const buildingData = []
    const citySize = 12

    for (let x = 0; x < citySize; x++) {
      for (let z = 0; z < citySize; z++) {
        if (Math.random() > 0.3) { // Not every spot has a building
          const height = 80 + Math.random() * 200
          const neonColors = ['#00ffff', '#ff0080', '#80ff00', '#ff8000', '#8000ff']

          buildingData.push({
            id: `${x}-${z}`,
            style: {
              left: `${x * 60 + Math.random() * 20}px`,
              top: `${z * 60 + Math.random() * 20}px`,
              width: `${30 + Math.random() * 25}px`,
              zIndex: Math.floor(height / 10)
            },
            height,
            neonColor: neonColors[Math.floor(Math.random() * neonColors.length)],
            hasAntenna: Math.random() > 0.7,
            windowPattern: Math.random() > 0.5 ? 'grid' : 'random'
          })
        }
      }
    }

    return buildingData.sort((a, b) => a.height - b.height)
  }, [])

  const vehicles = useMemo(() => [
    { id: 1, delay: 0, speed: 0.8, path: 'circular', vehicleType: 'standard' },
    { id: 2, delay: 2, speed: 1.2, path: 'figure8', vehicleType: 'police' },
    { id: 3, delay: 4, speed: 0.6, path: 'random', vehicleType: 'cargo' },
    { id: 4, delay: 1, speed: 1.0, path: 'circular', vehicleType: 'standard' },
    { id: 5, delay: 3, speed: 0.9, path: 'patrol', vehicleType: 'police' },
    { id: 6, delay: 5, speed: 1.1, path: 'figure8', vehicleType: 'standard' },
    { id: 7, delay: 6, speed: 0.7, path: 'circular', vehicleType: 'cargo' }
  ], [])

  const groundVehicles = useMemo(() => [
    { id: 1, lane: 0, direction: 1, speed: 1.2, vehicleType: 'car' },
    { id: 2, lane: 1, direction: -1, speed: 0.8, vehicleType: 'truck' },
    { id: 3, lane: 2, direction: 1, speed: 1.5, vehicleType: 'taxi' },
    { id: 4, lane: 0, direction: -1, speed: 1.0, vehicleType: 'car' },
    { id: 5, lane: 1, direction: 1, speed: 0.9, vehicleType: 'car' },
    { id: 6, lane: 2, direction: -1, speed: 1.3, vehicleType: 'taxi' }
  ], [])

  const pedestrians = useMemo(() => [
    { id: 1, startX: 100, targetX: 300, speed: 1 },
    { id: 2, startX: 500, targetX: 200, speed: 0.8 },
    { id: 3, startX: 700, targetX: 900, speed: 1.2 },
    { id: 4, startX: 400, targetX: 600, speed: 0.9 }
  ], [])

  const securityDrones = useMemo(() => [
    {
      id: 1,
      patrolArea: { centerX: 200, centerY: 300, width: 150, height: 100 }
    },
    {
      id: 2,
      patrolArea: { centerX: 600, centerY: 200, width: 200, height: 150 }
    }
  ], [])

  const billboards = useMemo(() => [
    {
      id: 1,
      text: 'NEON CORP',
      position: { left: '200px', top: '150px' },
      color: '#ff0080',
      size: 'large'
    },
    {
      id: 2,
      text: 'CYBER TECH',
      position: { left: '500px', top: '100px' },
      color: '#00ffff',
      size: 'medium'
    },
    {
      id: 3,
      text: 'NEURAL LINK',
      position: { left: '300px', top: '250px' },
      color: '#80ff00',
      size: 'large'
    },
    {
      id: 4,
      text: 'QUANTUM SYSTEMS',
      position: { right: '150px', top: '180px' },
      color: '#ff8000',
      size: 'medium'
    }
  ], [])

  const trafficLights = useMemo(() => [
    { id: 1, position: { left: '150px', bottom: '220px' } },
    { id: 2, position: { left: '350px', bottom: '220px' } },
    { id: 3, position: { left: '550px', bottom: '220px' } },
    { id: 4, position: { left: '750px', bottom: '220px' } }
  ], [])

  // City activity simulation
  useEffect(() => {
    const activityCycle = () => {
      const activities = ['normal', 'busy', 'rush_hour', 'quiet']
      const weights = timeOfDay === 'night' ? [0.4, 0.2, 0.1, 0.3] : [0.3, 0.3, 0.3, 0.1]

      let random = Math.random()
      let activity = 'normal'

      for (let i = 0; i < activities.length; i++) {
        random -= weights[i]
        if (random <= 0) {
          activity = activities[i]
          break
        }
      }

      setCityActivity(activity)
    }

    activityCycle()
    const interval = setInterval(activityCycle, 15000 + Math.random() * 10000)
    return () => clearInterval(interval)
  }, [timeOfDay])

  return (
    <div className={`cyberpunk-city ${timeOfDay} ${weatherEffect} ${cityActivity}`}>
      {/* Background layers */}
      <div className="city-background" />
      <div className="atmosphere-layer" />

      {/* Ground grid */}
      <div className="cyber-ground" />

      {/* Buildings */}
      <div className="buildings-container">
        {buildings.map(building => (
          <Building key={building.id} {...building} />
        ))}
      </div>

      {/* Flying vehicles */}
      <div className="vehicles-container">
        {vehicles.map(vehicle => (
          <FlyingVehicle key={vehicle.id} {...vehicle} />
        ))}
      </div>

      {/* Ground traffic */}
      <div className="ground-traffic-container">
        {groundVehicles.map(vehicle => (
          <GroundVehicle key={vehicle.id} {...vehicle} />
        ))}
      </div>

      {/* Pedestrians */}
      <div className="pedestrians-container">
        {pedestrians.map(pedestrian => (
          <Pedestrian key={pedestrian.id} {...pedestrian} />
        ))}
      </div>

      {/* Security drones */}
      <div className="drones-container">
        {securityDrones.map(drone => (
          <SecurityDrone key={drone.id} {...drone} />
        ))}
      </div>

      {/* Traffic lights */}
      <div className="traffic-lights-container">
        {trafficLights.map(light => (
          <TrafficLight key={light.id} {...light} />
        ))}
      </div>

      {/* Emergency events */}
      <EmergencyEvent />

      {/* Holographic billboards */}
      <div className="billboards-container">
        {billboards.map(billboard => (
          <HoloBillboard key={billboard.id} {...billboard} />
        ))}
      </div>

      {/* Weather effects */}
      {weatherEffect === 'rain' && <ParticleSystem type="rain" density={100} />}
      {weatherEffect === 'snow' && <ParticleSystem type="snow" density={50} />}

      {/* UI overlays */}
      <div className="ui-overlay">
        <div className="system-info">
          <div>NEURAL NETWORK: ONLINE</div>
          <div>CITY STATUS: {cityActivity.toUpperCase()}</div>
          <div>POPULATION: 2.4M</div>
          <div>SECURITY LEVEL: {cityActivity === 'rush_hour' ? 'ELEVATED' : 'HIGH'}</div>
          <div>WEATHER: {weatherEffect.toUpperCase()}</div>
          <div>TIME: {timeOfDay.toUpperCase()} MODE</div>
        </div>
      </div>

      {/* Scanlines effect */}
      <div className="scanlines" />

      {/* Control panel */}
      <div className="control-panel">
        <button
          onClick={() => setTimeOfDay(timeOfDay === 'night' ? 'day' : 'night')}
          className="cyber-button"
        >
          {timeOfDay === 'night' ? 'DAY MODE' : 'NIGHT MODE'}
        </button>
        <button
          onClick={() => setWeatherEffect(
            weatherEffect === 'clear' ? 'rain' :
            weatherEffect === 'rain' ? 'snow' : 'clear'
          )}
          className="cyber-button"
        >
          WEATHER: {weatherEffect.toUpperCase()}
        </button>
        <button
          onClick={() => setCityActivity(
            cityActivity === 'normal' ? 'busy' :
            cityActivity === 'busy' ? 'rush_hour' :
            cityActivity === 'rush_hour' ? 'quiet' : 'normal'
          )}
          className="cyber-button"
        >
          ACTIVITY: {cityActivity.toUpperCase()}
        </button>
        <button
          onClick={() => setSoundEnabled(!soundEnabled)}
          className="cyber-button"
        >
          SOUND: {soundEnabled ? 'ON' : 'OFF'}
        </button>
      </div>
    </div>
  )
}


