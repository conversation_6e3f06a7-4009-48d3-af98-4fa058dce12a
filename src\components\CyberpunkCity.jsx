import React, { useRef, useMemo, useEffect, useState } from 'react'
import './CyberpunkCity.css'

// Building component with detailed CSS-based 3D effect
function Building({ style, height, neonColor, hasAntenna = true, windowPattern = 'grid' }) {
  const [glitchActive, setGlitchActive] = useState(false)

  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.95) {
        setGlitchActive(true)
        setTimeout(() => setGlitchActive(false), 200)
      }
    }, 1000)
    return () => clearInterval(interval)
  }, [])

  const windows = useMemo(() => {
    const windowArray = []
    const floors = Math.floor(height / 20)
    const windowsPerFloor = 4 + Math.floor(Math.random() * 4)

    for (let floor = 0; floor < floors; floor++) {
      for (let window = 0; window < windowsPerFloor; window++) {
        windowArray.push({
          id: `${floor}-${window}`,
          lit: Math.random() > 0.3,
          color: Math.random() > 0.8 ? '#ff0080' : '#ffff80'
        })
      }
    }
    return windowArray
  }, [height])

  return (
    <div
      className={`building ${glitchActive ? 'glitch' : ''}`}
      style={{
        ...style,
        height: `${height}px`,
        '--neon-color': neonColor
      }}
    >
      <div className="building-structure">
        <div className="building-face front">
          <div className="windows">
            {windows.map((window, index) => (
              <div
                key={window.id}
                className={`window ${window.lit ? 'lit' : 'dark'}`}
                style={{
                  backgroundColor: window.lit ? window.color : '#001122',
                  boxShadow: window.lit ? `0 0 10px ${window.color}` : 'none'
                }}
              />
            ))}
          </div>
          <div className="neon-strips">
            <div className="neon-strip horizontal top" />
            <div className="neon-strip vertical left" />
            <div className="neon-strip vertical right" />
          </div>
        </div>

        {hasAntenna && (
          <div className="antenna">
            <div className="antenna-pole" />
            <div className="antenna-light blinking" />
          </div>
        )}

        <div className="building-glow" />
      </div>
    </div>
  )
}

// Flying vehicle component
function FlyingVehicle({ delay = 0, speed = 1, path = 'circular' }) {
  const [position, setPosition] = useState({ x: 0, y: 0, z: 0 })

  useEffect(() => {
    let animationId
    const startTime = Date.now() + delay * 1000

    const animate = () => {
      const elapsed = (Date.now() - startTime) / 1000
      if (elapsed > 0) {
        const time = elapsed * speed

        let x, y, z
        if (path === 'circular') {
          x = Math.sin(time * 0.5) * 300
          z = Math.cos(time * 0.3) * 250
          y = 100 + Math.sin(time * 2) * 30
        } else if (path === 'figure8') {
          x = Math.sin(time * 0.8) * 200
          z = Math.sin(time * 0.4) * 300
          y = 120 + Math.cos(time * 1.5) * 20
        } else {
          x = Math.sin(time * 0.3) * 350
          z = Math.cos(time * 0.7) * 200
          y = 80 + Math.sin(time * 3) * 40
        }

        setPosition({ x, y, z })
      }
      animationId = requestAnimationFrame(animate)
    }

    animate()
    return () => cancelAnimationFrame(animationId)
  }, [delay, speed, path])

  return (
    <div
      className="flying-vehicle"
      style={{
        transform: `translate3d(${position.x}px, ${-position.y}px, ${position.z}px)`,
        '--vehicle-glow': Math.random() > 0.5 ? '#00ff00' : '#ff0080'
      }}
    >
      <div className="vehicle-body" />
      <div className="vehicle-lights">
        <div className="light front" />
        <div className="light rear" />
      </div>
      <div className="vehicle-trail" />
    </div>
  )
}

// Holographic billboard component
function HoloBillboard({ text, position, color = '#00ffff', size = 'large' }) {
  const [glitchText, setGlitchText] = useState(text)
  const [isGlitching, setIsGlitching] = useState(false)

  useEffect(() => {
    const glitchInterval = setInterval(() => {
      if (Math.random() > 0.9) {
        setIsGlitching(true)
        const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'
        let glitched = text.split('').map(char =>
          Math.random() > 0.7 ? glitchChars[Math.floor(Math.random() * glitchChars.length)] : char
        ).join('')
        setGlitchText(glitched)

        setTimeout(() => {
          setGlitchText(text)
          setIsGlitching(false)
        }, 150)
      }
    }, 2000)

    return () => clearInterval(glitchInterval)
  }, [text])

  return (
    <div
      className={`holo-billboard ${size} ${isGlitching ? 'glitching' : ''}`}
      style={{
        ...position,
        '--billboard-color': color
      }}
    >
      <div className="billboard-frame" />
      <div className="billboard-content">
        <div className="billboard-text">{glitchText}</div>
        <div className="billboard-scanlines" />
      </div>
      <div className="billboard-glow" />
    </div>
  )
}

// Particle system for atmospheric effects
function ParticleSystem({ type = 'rain', density = 50 }) {
  const [particles, setParticles] = useState([])

  useEffect(() => {
    const newParticles = []
    for (let i = 0; i < density; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight,
        speed: 1 + Math.random() * 3,
        opacity: 0.3 + Math.random() * 0.7,
        size: type === 'rain' ? 1 + Math.random() * 2 : 2 + Math.random() * 4
      })
    }
    setParticles(newParticles)
  }, [density, type])

  useEffect(() => {
    const animateParticles = () => {
      setParticles(prev => prev.map(particle => ({
        ...particle,
        y: particle.y > window.innerHeight ? -10 : particle.y + particle.speed,
        x: type === 'snow' ? particle.x + Math.sin(Date.now() * 0.001 + particle.id) * 0.5 : particle.x
      })))
    }

    const interval = setInterval(animateParticles, 50)
    return () => clearInterval(interval)
  }, [type])

  return (
    <div className="particle-system">
      {particles.map(particle => (
        <div
          key={particle.id}
          className={`particle ${type}`}
          style={{
            left: `${particle.x}px`,
            top: `${particle.y}px`,
            opacity: particle.opacity,
            width: `${particle.size}px`,
            height: type === 'rain' ? `${particle.size * 10}px` : `${particle.size}px`
          }}
        />
      ))}
    </div>
  )
}

// Main cyberpunk city component
export default function CyberpunkCity() {
  const [timeOfDay, setTimeOfDay] = useState('night')
  const [weatherEffect, setWeatherEffect] = useState('clear')

  const buildings = useMemo(() => {
    const buildingData = []
    const citySize = 12

    for (let x = 0; x < citySize; x++) {
      for (let z = 0; z < citySize; z++) {
        if (Math.random() > 0.3) { // Not every spot has a building
          const height = 80 + Math.random() * 200
          const neonColors = ['#00ffff', '#ff0080', '#80ff00', '#ff8000', '#8000ff']

          buildingData.push({
            id: `${x}-${z}`,
            style: {
              left: `${x * 60 + Math.random() * 20}px`,
              top: `${z * 60 + Math.random() * 20}px`,
              width: `${30 + Math.random() * 25}px`,
              zIndex: Math.floor(height / 10)
            },
            height,
            neonColor: neonColors[Math.floor(Math.random() * neonColors.length)],
            hasAntenna: Math.random() > 0.7,
            windowPattern: Math.random() > 0.5 ? 'grid' : 'random'
          })
        }
      }
    }

    return buildingData.sort((a, b) => a.height - b.height)
  }, [])

  const vehicles = useMemo(() => [
    { id: 1, delay: 0, speed: 0.8, path: 'circular' },
    { id: 2, delay: 2, speed: 1.2, path: 'figure8' },
    { id: 3, delay: 4, speed: 0.6, path: 'random' },
    { id: 4, delay: 1, speed: 1.0, path: 'circular' },
    { id: 5, delay: 3, speed: 0.9, path: 'figure8' }
  ], [])

  const billboards = useMemo(() => [
    {
      id: 1,
      text: 'NEON CORP',
      position: { left: '200px', top: '150px' },
      color: '#ff0080',
      size: 'large'
    },
    {
      id: 2,
      text: 'CYBER TECH',
      position: { left: '500px', top: '100px' },
      color: '#00ffff',
      size: 'medium'
    },
    {
      id: 3,
      text: 'NEURAL LINK',
      position: { left: '300px', top: '250px' },
      color: '#80ff00',
      size: 'large'
    },
    {
      id: 4,
      text: 'QUANTUM SYSTEMS',
      position: { right: '150px', top: '180px' },
      color: '#ff8000',
      size: 'medium'
    }
  ], [])

  return (
    <div className={`cyberpunk-city ${timeOfDay} ${weatherEffect}`}>
      {/* Background layers */}
      <div className="city-background" />
      <div className="atmosphere-layer" />

      {/* Ground grid */}
      <div className="cyber-ground" />

      {/* Buildings */}
      <div className="buildings-container">
        {buildings.map(building => (
          <Building key={building.id} {...building} />
        ))}
      </div>

      {/* Flying vehicles */}
      <div className="vehicles-container">
        {vehicles.map(vehicle => (
          <FlyingVehicle key={vehicle.id} {...vehicle} />
        ))}
      </div>

      {/* Holographic billboards */}
      <div className="billboards-container">
        {billboards.map(billboard => (
          <HoloBillboard key={billboard.id} {...billboard} />
        ))}
      </div>

      {/* Weather effects */}
      {weatherEffect === 'rain' && <ParticleSystem type="rain" density={100} />}
      {weatherEffect === 'snow' && <ParticleSystem type="snow" density={50} />}

      {/* UI overlays */}
      <div className="ui-overlay">
        <div className="system-info">
          <div>NEURAL NETWORK: ONLINE</div>
          <div>CITY STATUS: OPERATIONAL</div>
          <div>POPULATION: 2.4M</div>
          <div>SECURITY LEVEL: HIGH</div>
        </div>
      </div>

      {/* Scanlines effect */}
      <div className="scanlines" />

      {/* Control panel */}
      <div className="control-panel">
        <button
          onClick={() => setTimeOfDay(timeOfDay === 'night' ? 'day' : 'night')}
          className="cyber-button"
        >
          {timeOfDay === 'night' ? 'DAY MODE' : 'NIGHT MODE'}
        </button>
        <button
          onClick={() => setWeatherEffect(
            weatherEffect === 'clear' ? 'rain' :
            weatherEffect === 'rain' ? 'snow' : 'clear'
          )}
          className="cyber-button"
        >
          WEATHER: {weatherEffect.toUpperCase()}
        </button>
      </div>
    </div>
  )
}


