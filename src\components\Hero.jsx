import React from 'react'
import './Hero.css'

const Hero = () => {
  return (
    <section id="home" className="hero">
      <div className="hero-bg">
        <div className="hero-pattern"></div>
        <div className="hero-gradient"></div>
      </div>
      
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title fade-in-up">
              Crafting Digital
              <span className="gradient-text"> Experiences</span>
              <br />
              That Inspire
            </h1>
            
            <p className="hero-subtitle fade-in-up">
              We are Teranium Design, a creative agency specializing in modern web design, 
              branding, and digital experiences that captivate audiences and drive results.
            </p>
            
            <div className="hero-actions fade-in-up">
              <a href="#portfolio" className="btn btn-primary">
                View Our Work
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </a>
              <a href="#about" className="btn btn-secondary">
                Learn More
              </a>
            </div>
            
            <div className="hero-stats fade-in-up">
              <div className="stat">
                <div className="stat-number">150+</div>
                <div className="stat-label">Projects Completed</div>
              </div>
              <div className="stat">
                <div className="stat-number">50+</div>
                <div className="stat-label">Happy Clients</div>
              </div>
              <div className="stat">
                <div className="stat-number">5+</div>
                <div className="stat-label">Years Experience</div>
              </div>
            </div>
          </div>
          
          <div className="hero-visual">
            <div className="hero-card">
              <div className="card-header">
                <div className="card-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
              <div className="card-content">
                <div className="design-elements">
                  <div className="element element-1"></div>
                  <div className="element element-2"></div>
                  <div className="element element-3"></div>
                  <div className="element element-4"></div>
                </div>
              </div>
            </div>
            
            <div className="floating-elements">
              <div className="floating-element floating-1"></div>
              <div className="floating-element floating-2"></div>
              <div className="floating-element floating-3"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
