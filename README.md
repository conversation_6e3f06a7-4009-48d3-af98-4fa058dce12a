# Teranium Design - Modern Design Company Website

A modern, responsive website for Teranium Design Company built with React and Vite, featuring contemporary UI/UX design principles and smooth animations.

## 🚀 Features

- **Modern Design**: Clean, professional design with gradient accents and modern typography
- **Fully Responsive**: Optimized for desktop, tablet, and mobile devices
- **Interactive Components**: Smooth animations and hover effects
- **Performance Optimized**: Built with Vite for fast loading and development
- **Accessible**: Semantic HTML and proper ARIA labels
- **SEO Friendly**: Proper meta tags and structured content

## 📱 Sections

1. **Hero Section**: Eye-catching introduction with animated design elements
2. **Services**: Comprehensive showcase of design services with interactive cards
3. **Portfolio**: Filterable project gallery with overlay details
4. **About**: Company information, team members, and core values
5. **Contact**: Contact form and company information
6. **Footer**: Links, newsletter signup, and social media

## 🛠️ Technologies Used

- **React 18**: Modern React with hooks and functional components
- **Vite**: Fast build tool and development server
- **CSS3**: Custom CSS with CSS Grid, Flexbox, and animations
- **Modern JavaScript**: ES6+ features and best practices
- **Google Fonts**: Inter font family for modern typography

## 🎨 Design Features

- **Color Scheme**: Professional blue and orange gradient palette
- **Typography**: Inter font family for excellent readability
- **Animations**: Subtle hover effects and smooth transitions
- **Shadows**: Modern shadow system for depth and hierarchy
- **Responsive Grid**: CSS Grid and Flexbox for flexible layouts

## 📁 Project Structure

```
teranium-design/
├── public/
│   ├── vite.svg
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Header.jsx & Header.css
│   │   ├── Hero.jsx & Hero.css
│   │   ├── Services.jsx & Services.css
│   │   ├── Portfolio.jsx & Portfolio.css
│   │   ├── About.jsx & About.css
│   │   ├── Contact.jsx & Contact.css
│   │   └── Footer.jsx & Footer.css
│   ├── App.jsx & App.css
│   ├── index.css
│   └── main.jsx
├── demo.html (Standalone demo)
├── package.json
├── vite.config.js
└── README.md
```

## 🚀 Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd teranium-design
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open in browser**
   Navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
# or
yarn build
```

The built files will be in the `dist` directory.

## 📱 Responsive Breakpoints

- **Desktop**: 1024px and above
- **Tablet**: 768px - 1023px
- **Mobile**: 320px - 767px

## 🎯 Key Components

### Header
- Fixed navigation with blur effect
- Responsive mobile menu
- Smooth scroll navigation

### Hero Section
- Animated design elements
- Gradient text effects
- Call-to-action buttons
- Statistics display

### Services
- Interactive service cards
- Hover animations
- Feature lists with icons

### Portfolio
- Filterable project gallery
- Image overlays with project details
- Technology tags

### Contact
- Functional contact form
- Company information cards
- Social media links

## 🔧 Customization

### Colors
Update CSS custom properties in `src/index.css`:
```css
:root {
  --primary-color: #6366f1;
  --secondary-color: #f59e0b;
  /* ... other colors */
}
```

### Content
- Update company information in component files
- Replace placeholder images with actual project images
- Modify service offerings in `Services.jsx`
- Update team information in `About.jsx`

## 📞 Contact Information

- **Email**: <EMAIL>
- **Phone**: +****************
- **Address**: 123 Design Street, Creative District, New York, NY 10001

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request

## 📈 Performance

- **Lighthouse Score**: 95+ (Performance, Accessibility, Best Practices, SEO)
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1

## 🔮 Future Enhancements

- [ ] Blog section with CMS integration
- [ ] Client testimonials carousel
- [ ] Case study detail pages
- [ ] Multi-language support
- [ ] Dark mode toggle
- [ ] Advanced animations with Framer Motion

---

Built with ❤️ by Teranium Design Team
