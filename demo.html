<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teranium Design - Modern Design Solutions</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #f59e0b;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-dark: #111827;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }

        html {
            scroll-behavior: smooth;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .section-padding {
            padding: 5rem 0;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .gradient-text {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
            min-height: 70px;
        }

        .logo {
            display: flex;
            align-items: baseline;
            gap: 0.5rem;
        }

        .logo-text {
            font-size: 1.75rem;
            font-weight: 700;
            margin: 0;
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Hero Styles */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding-top: 70px;
            overflow: hidden;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(245, 158, 11, 0.02) 100%);
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            min-height: calc(100vh - 140px);
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            line-height: 1.7;
            margin-bottom: 2.5rem;
            max-width: 500px;
        }

        .hero-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .hero-visual {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-card {
            background: var(--bg-primary);
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            width: 400px;
            height: 300px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            position: relative;
            transform: rotate(-5deg);
            transition: transform 0.3s ease;
        }

        .hero-card:hover {
            transform: rotate(-2deg) scale(1.02);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                gap: 3rem;
                text-align: center;
                padding: 2rem 0;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.125rem;
                max-width: none;
            }
            
            .hero-card {
                width: 300px;
                height: 200px;
            }
        }

        @media (max-width: 480px) {
            .hero-title {
                font-size: 2rem;
                line-height: 1.2;
            }
            
            .hero-subtitle {
                font-size: 1rem;
                margin-bottom: 2rem;
            }
            
            .hero-actions {
                flex-direction: column;
                align-items: center;
                gap: 0.75rem;
            }
            
            .hero-card {
                width: 250px;
                height: 180px;
            }
        }

        /* Demo content styles */
        .demo-section {
            padding: 3rem 0;
            background: var(--bg-secondary);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .demo-card {
            background: var(--bg-primary);
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-align: center;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            text-align: center;
            max-width: 600px;
            margin: 0 auto 3rem;
            line-height: 1.7;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1 class="logo-text gradient-text">Teranium</h1>
                    <span class="logo-subtitle">Design</span>
                </div>
                <a href="#contact" class="btn btn-primary">Get Started</a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        Crafting Digital
                        <span class="gradient-text"> Experiences</span>
                        <br />
                        That Inspire
                    </h1>
                    
                    <p class="hero-subtitle">
                        We are Teranium Design, a creative agency specializing in modern web design, 
                        branding, and digital experiences that captivate audiences and drive results.
                    </p>
                    
                    <div class="hero-actions">
                        <a href="#portfolio" class="btn btn-primary">
                            View Our Work
                        </a>
                        <a href="#about" class="btn btn-primary" style="background: transparent; color: var(--primary-color); border: 2px solid var(--primary-color);">
                            Learn More
                        </a>
                    </div>
                </div>
                
                <div class="hero-visual">
                    <div class="hero-card">
                        <div style="background: var(--bg-secondary); padding: 1rem; border-bottom: 1px solid var(--border-color);">
                            <div style="display: flex; gap: 0.5rem;">
                                <span style="width: 12px; height: 12px; border-radius: 50%; background: #ef4444;"></span>
                                <span style="width: 12px; height: 12px; border-radius: 50%; background: #f59e0b;"></span>
                                <span style="width: 12px; height: 12px; border-radius: 50%; background: #10b981;"></span>
                            </div>
                        </div>
                        <div style="padding: 2rem; height: calc(100% - 60px); position: relative;">
                            <div style="position: absolute; width: 80px; height: 20px; background: linear-gradient(90deg, var(--primary-color), var(--primary-dark)); top: 20%; left: 10%; border-radius: 0.5rem;"></div>
                            <div style="position: absolute; width: 60px; height: 60px; background: linear-gradient(135deg, var(--secondary-color), #f97316); top: 40%; right: 20%; border-radius: 50%;"></div>
                            <div style="position: absolute; width: 100px; height: 15px; background: linear-gradient(90deg, #10b981, #059669); bottom: 30%; left: 20%; border-radius: 0.5rem;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Services Section -->
    <section class="demo-section">
        <div class="container">
            <h2 class="section-title">Our Services</h2>
            <p class="section-subtitle">
                We offer comprehensive design and development services to help your business 
                stand out in the digital world and achieve remarkable growth.
            </p>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;">Web Design</h3>
                    <p>Modern, responsive websites that captivate your audience and drive conversions.</p>
                </div>
                <div class="demo-card">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;">Brand Identity</h3>
                    <p>Comprehensive branding solutions that establish your unique identity.</p>
                </div>
                <div class="demo-card">
                    <h3 style="color: var(--primary-color); margin-bottom: 1rem;">UI/UX Design</h3>
                    <p>Intuitive user interfaces and seamless user experiences.</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Simple scroll effect for header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = 'var(--shadow-md)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>
