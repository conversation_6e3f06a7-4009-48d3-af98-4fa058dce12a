.contact {
  background: var(--bg-primary);
  position: relative;
}

.contact-header {
  text-align: center;
  margin-bottom: 4rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-form {
  background: var(--bg-secondary);
  padding: 2.5rem;
  border-radius: 1.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: 0.75rem;
  color: white;
  flex-shrink: 0;
}

.info-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.info-content p {
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
  line-height: 1.5;
}

.contact-cta {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-primary));
  padding: 2rem;
  border-radius: 1rem;
  border: 1px solid var(--border-color);
  margin-top: 2rem;
  text-align: center;
}

.contact-cta h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.contact-cta p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-form {
    padding: 2rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-group input,
  .form-group textarea {
    padding: 0.625rem 0.875rem;
  }

  .info-item {
    gap: 0.75rem;
  }

  .info-icon {
    width: 40px;
    height: 40px;
  }

  .info-icon svg {
    width: 20px;
    height: 20px;
  }

  .info-content h4 {
    font-size: 1rem;
  }

  .info-content p {
    font-size: 0.875rem;
  }

  .contact-cta {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }

  .contact-cta h3 {
    font-size: 1.25rem;
  }

  .contact-cta p {
    font-size: 0.875rem;
  }

  .social-links a {
    width: 36px;
    height: 36px;
  }

  .social-links a svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 480px) {
  .contact-header {
    margin-bottom: 2rem;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .form-group input,
  .form-group textarea {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .submit-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  .contact-info {
    gap: 1.5rem;
  }

  .contact-cta {
    padding: 1rem;
  }

  .social-links {
    gap: 0.75rem;
  }
}
