<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk City - Animated Miniature Metropolis</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Courier New', monospace;
            background: radial-gradient(ellipse at center, #001122 0%, #000011 70%, #000000 100%);
            min-height: 100vh;
            overflow: hidden;
            color: #00ffff;
        }
        
        .demo-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            perspective: 1000px;
            transform-style: preserve-3d;
        }
        
        .city-preview {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 30px;
            border: 2px solid #00ffff;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 0 30px #00ffff;
        }
        
        .city-preview h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px #00ffff;
            animation: titleGlow 2s ease-in-out infinite alternate;
        }
        
        .city-preview p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .cyber-btn {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(45deg, #00ffff, #ff0080);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: bold;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
            border: none;
            cursor: pointer;
            text-shadow: 0 0 10px rgba(0,0,0,0.5);
            margin: 10px;
        }
        
        .cyber-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 0 40px rgba(0, 255, 255, 0.8);
            filter: brightness(1.2);
        }
        
        .background-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 10%, rgba(128, 255, 0, 0.05) 0%, transparent 50%);
            animation: atmosphericShift 15s ease-in-out infinite;
        }
        
        .grid-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                linear-gradient(90deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%),
                linear-gradient(0deg, transparent 49%, rgba(0, 255, 255, 0.1) 50%, transparent 51%);
            background-size: 50px 50px;
            animation: gridPulse 3s ease-in-out infinite;
            opacity: 0.3;
        }
        
        .scanlines {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                transparent 50%, 
                rgba(0, 255, 255, 0.03) 50%
            );
            background-size: 100% 4px;
            pointer-events: none;
            animation: scanlineMove 0.1s linear infinite;
        }
        
        .features-list {
            text-align: left;
            margin: 20px 0;
            font-size: 14px;
        }
        
        .features-list li {
            margin: 8px 0;
            list-style: none;
            position: relative;
            padding-left: 20px;
        }
        
        .features-list li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #ff0080;
            text-shadow: 0 0 5px #ff0080;
        }
        
        @keyframes titleGlow {
            0% { text-shadow: 0 0 20px #00ffff; }
            100% { text-shadow: 0 0 30px #00ffff, 0 0 40px #00ffff; }
        }
        
        @keyframes atmosphericShift {
            0%, 100% { filter: hue-rotate(0deg); }
            33% { filter: hue-rotate(120deg); }
            66% { filter: hue-rotate(240deg); }
        }
        
        @keyframes gridPulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }
        
        @keyframes scanlineMove {
            0% { transform: translateY(0); }
            100% { transform: translateY(4px); }
        }
        
        @keyframes sparkleAnim {
            0% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0) translateY(-20px); }
        }
        
        @media (max-width: 768px) {
            .city-preview {
                padding: 20px;
                margin: 20px;
            }
            
            .city-preview h1 {
                font-size: 2rem;
            }
            
            .cyber-btn {
                padding: 12px 25px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="background-animation"></div>
        <div class="grid-overlay"></div>
        <div class="scanlines"></div>
        
        <div class="city-preview">
            <h1>🌃 CYBERPUNK CITY 🌃</h1>
            <p>Experience a detailed animated miniature cyberpunk metropolis</p>
            
            <ul class="features-list">
                <li>Procedurally generated buildings with neon lighting</li>
                <li>Flying vehicles with dynamic flight paths</li>
                <li>Holographic billboards with glitch effects</li>
                <li>Weather systems (rain, snow, clear)</li>
                <li>Day/night cycle transitions</li>
                <li>Atmospheric particle effects</li>
                <li>Interactive UI with cyberpunk aesthetics</li>
                <li>Responsive design for all devices</li>
            </ul>
            
            <button class="cyber-btn" onclick="window.location.href='index.html'">
                🚀 ENTER THE CITY
            </button>
            
            <button class="cyber-btn" onclick="showFullscreen()">
                🎮 FULLSCREEN MODE
            </button>
        </div>
    </div>
    
    <script>
        function showFullscreen() {
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            }
            window.location.href = 'index.html';
        }
        
        // Add some interactive sparkles
        document.addEventListener('mousemove', function(e) {
            if (Math.random() > 0.95) {
                createSparkle(e.clientX, e.clientY);
            }
        });
        
        function createSparkle(x, y) {
            const sparkle = document.createElement('div');
            sparkle.style.position = 'fixed';
            sparkle.style.left = x + 'px';
            sparkle.style.top = y + 'px';
            sparkle.style.width = '4px';
            sparkle.style.height = '4px';
            sparkle.style.background = '#00ffff';
            sparkle.style.borderRadius = '50%';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.boxShadow = '0 0 10px #00ffff';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkleAnim 1s ease-out forwards';
            
            document.body.appendChild(sparkle);
            
            setTimeout(() => {
                sparkle.remove();
            }, 1000);
        }
    </script>
</body>
</html>
